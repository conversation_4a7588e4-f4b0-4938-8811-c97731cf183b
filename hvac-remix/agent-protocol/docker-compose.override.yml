# Docker Compose Override for Development
# This file removes GPU requirements for development environments

version: '3.8'

services:
  # Bielik V3 - CPU only version for development
  bielik-v3:
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_MODELS=/root/.ollama/models
      - OLLAMA_NUM_PARALLEL=1
      - OLLAMA_MAX_LOADED_MODELS=1

  # Gemma4 - CPU only version for development  
  gemma4:
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_MODELS=/root/.ollama/models
      - OLLAMA_NUM_PARALLEL=1
      - OLLAMA_MAX_LOADED_MODELS=1

  # Agent Protocol Server - Development configuration
  agent-protocol-server:
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
      - BIELIK_API_URL=http://bielik-v3:11434
      - GEMMA_API_URL=http://gemma4:11434
      - QDRANT_URL=http://qdrant:6333
      - POSTGRES_URL=*****************************************************************/agent_protocol
      - REDIS_URL=redis://redis:6379
      - DEBUG=true
      - LOG_LEVEL=DEBUG
