#!/usr/bin/env python3
"""
Test script for LLM endpoints in the HVAC Agent Protocol system.
"""

import requests
import json
import time
import sys

def test_endpoint(name, url, timeout=5):
    """Test if an endpoint is responding."""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"✅ {name}: OK")
            return True
        else:
            print(f"❌ {name}: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ {name}: {e}")
        return False

def test_ollama_models(name, base_url):
    """Test Ollama models endpoint."""
    try:
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"✅ {name}: {len(models)} models available")
            for model in models:
                print(f"   - {model.get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ {name}: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ {name}: {e}")
        return False

def test_llm_generation(name, base_url, model_name):
    """Test LLM text generation."""
    try:
        payload = {
            "model": model_name,
            "prompt": "What is HVAC?",
            "stream": False
        }
        
        response = requests.post(
            f"{base_url}/api/generate",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            print(f"✅ {name} Generation: OK")
            print(f"   Response: {response_text[:100]}...")
            return True
        else:
            print(f"❌ {name} Generation: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ {name} Generation: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Testing HVAC Agent Protocol LLM Endpoints")
    print("=" * 50)
    
    # Test basic endpoints
    endpoints = [
        ("Agent Protocol API", "http://localhost:8001/"),
        ("Agent Protocol Health", "http://localhost:8001/health"),
        ("Bielik V3 API", "http://localhost:8877/api/tags"),
        ("Gemma4 API", "http://localhost:8878/api/tags"),
        ("Qdrant Health", "http://localhost:6333/health"),
        ("PostgreSQL", "http://localhost:5432"),  # This will fail but shows if port is open
        ("Redis", "http://localhost:6379"),  # This will fail but shows if port is open
    ]
    
    print("\n📡 Testing Basic Endpoints:")
    print("-" * 30)
    
    results = []
    for name, url in endpoints:
        if "5432" in url or "6379" in url:
            # Skip direct HTTP tests for DB ports
            continue
        results.append(test_endpoint(name, url))
    
    # Test Ollama models
    print("\n🤖 Testing LLM Models:")
    print("-" * 25)
    
    bielik_models = test_ollama_models("Bielik V3", "http://localhost:8877")
    gemma_models = test_ollama_models("Gemma4", "http://localhost:8878")
    
    # Test text generation (if models are available)
    print("\n💬 Testing Text Generation:")
    print("-" * 30)
    
    if bielik_models:
        test_llm_generation("Bielik V3", "http://localhost:8877", "bielik:7b")
    
    if gemma_models:
        test_llm_generation("Gemma4", "http://localhost:8878", "gemma2:9b")
    
    # Summary
    print("\n📊 Test Summary:")
    print("-" * 20)
    
    success_count = sum(results)
    total_tests = len(results)
    
    if success_count == total_tests:
        print(f"✅ All tests passed ({success_count}/{total_tests})")
        return 0
    else:
        print(f"❌ Some tests failed ({success_count}/{total_tests})")
        return 1

if __name__ == "__main__":
    sys.exit(main())
