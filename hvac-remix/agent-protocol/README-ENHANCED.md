# HVAC Agent Protocol Enhanced System

## 🚀 Overview

This enhanced version of the HVAC Agent Protocol includes integrated **Bielik V3** and **Gemma4** LLM containers, providing a complete AI-powered HVAC CRM solution with local language model capabilities.

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    HVAC Agent Protocol                     │
│                   Enhanced Architecture                    │
└─────────────────────────────────────────────────────────────┘

┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Bielik V3     │  │    Gemma4       │  │ Agent Protocol  │
│   (Port 8877)   │  │  (Port 8878)    │  │   (Port 8001)   │
│                 │  │                 │  │                 │
│ • Polish LLM    │  │ • Advanced      │  │ • FastAPI       │
│ • HVAC Domain   │  │   Reasoning     │  │ • OpenAPI 3.1   │
│ • 7B Model      │  │ • 9B/27B Model  │  │ • Agent Mgmt    │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                        │
├─────────────────┬─────────────────┬─────────────────────────┤
│   PostgreSQL    │     Redis       │       Qdrant           │
│  (Port 5432)    │  (Port 6379)    │    (Port 6333)         │
│                 │                 │                        │
│ • Agent Data    │ • Caching       │ • Vector Storage       │
│ • Threads       │ • Sessions      │ • Embeddings          │
│ • Runs          │ • Queues        │ • Semantic Search     │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 🔧 Components

### Core Services

1. **Agent Protocol Server** (Port 8001)
   - FastAPI-based server
   - OpenAPI 3.1.0 specification
   - Agent management and execution

2. **Bielik V3 LLM** (Port 8877)
   - Polish language model
   - HVAC domain optimization
   - 7B parameter model
   - Ollama-based deployment

3. **Gemma4 LLM** (Port 8878)
   - Google's Gemma 2 model
   - Advanced reasoning capabilities
   - 9B/27B parameter variants
   - Technical analysis focus

### Infrastructure

4. **PostgreSQL** (Port 5432)
   - Primary data storage
   - Agent configurations
   - Thread and run history

5. **Redis** (Port 6379)
   - Caching layer
   - Session management
   - Task queues

6. **Qdrant** (Port 6333)
   - Vector database
   - Embedding storage
   - Semantic search

7. **Nginx** (Ports 80/443)
   - Reverse proxy
   - Load balancing
   - SSL termination

## 🚀 Quick Start

### Prerequisites

- Docker & Docker Compose
- 16GB+ RAM (recommended)
- NVIDIA GPU (optional, for acceleration)

### Installation

1. **Clone and navigate to the project:**
   ```bash
   cd hvac-remix/agent-protocol
   ```

2. **Run the enhanced setup:**
   ```bash
   ./setup-enhanced-system.sh
   ```

3. **Wait for model downloads** (this may take 30-60 minutes)

4. **Verify the installation:**
   ```bash
   python test-llm-endpoints.py
   ```

### Manual Setup

If you prefer manual control:

```bash
# Stop existing containers
docker-compose down

# Start infrastructure
docker-compose up -d postgres redis qdrant

# Start LLM services
docker-compose up -d bielik-v3 gemma4

# Download models
docker-compose run --rm llm-manager

# Start agent protocol
docker-compose up -d agent-protocol-server nginx
```

## 📡 API Endpoints

### Agent Protocol API
- **Base URL**: http://localhost:8001
- **Documentation**: http://localhost:8001/docs
- **Health Check**: http://localhost:8001/health

### LLM APIs
- **Bielik V3**: http://localhost:8877/api/
- **Gemma4**: http://localhost:8878/api/

### Infrastructure
- **Qdrant**: http://localhost:6333
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

## 🤖 Using the LLMs

### Bielik V3 (Polish HVAC Specialist)

```bash
curl -X POST http://localhost:8877/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "bielik:7b",
    "prompt": "Jak zdiagnozować problem z klimatyzacją?",
    "stream": false
  }'
```

### Gemma4 (Technical Analysis)

```bash
curl -X POST http://localhost:8878/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemma2:9b",
    "prompt": "Analyze HVAC system efficiency data and suggest optimizations",
    "stream": false
  }'
```

## 🔧 Configuration

### Environment Variables

```bash
# LLM Configuration
BIELIK_API_URL=http://bielik-v3:8877
GEMMA_API_URL=http://gemma4:8878

# Database Configuration
POSTGRES_URL=*****************************************************************/agent_protocol
REDIS_URL=redis://redis:6379
QDRANT_URL=http://qdrant:6333

# Development Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG
```

### Model Configuration

Models are configured in `llm-configs/`:
- `bielik/config.json` - Bielik V3 settings
- `gemma/config.json` - Gemma4 settings

## 📊 Monitoring

### Container Status
```bash
docker-compose ps
```

### Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f bielik-v3
docker-compose logs -f agent-protocol-server
```

### Resource Usage
```bash
docker stats
```

## 🛠️ Management Commands

### Update Models
```bash
docker-compose run --rm llm-manager
```

### Restart Services
```bash
docker-compose restart
```

### Clean Restart
```bash
docker-compose down
docker-compose up -d
```

### Backup Data
```bash
# Backup PostgreSQL
docker-compose exec postgres pg_dump -U agent_protocol agent_protocol > backup.sql

# Backup Qdrant
docker-compose exec qdrant tar -czf /tmp/qdrant-backup.tar.gz /qdrant/storage
```

## 🔍 Troubleshooting

### Common Issues

1. **Models not downloading**
   - Check internet connection
   - Verify Ollama containers are running
   - Check disk space (models are large)

2. **Out of memory errors**
   - Reduce model size in configurations
   - Increase Docker memory limits
   - Use CPU-only mode

3. **GPU not detected**
   - Install NVIDIA Docker runtime
   - Check GPU drivers
   - Use override file for CPU-only mode

### Debug Mode

Enable debug logging:
```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
docker-compose up -d
```

## 📈 Performance Optimization

### GPU Acceleration
- Install NVIDIA Docker runtime
- Use GPU-optimized models
- Monitor GPU memory usage

### Memory Management
- Adjust `OLLAMA_MAX_LOADED_MODELS`
- Use model quantization
- Implement model swapping

### Scaling
- Use multiple GPU containers
- Implement load balancing
- Add model caching

## 🔐 Security

### Production Deployment
- Change default passwords
- Enable SSL/TLS
- Configure firewall rules
- Use secrets management

### API Security
- Implement authentication
- Add rate limiting
- Enable CORS properly
- Monitor access logs

## 📚 Next Steps

1. **Integrate with HVAC-Remix frontend**
2. **Implement custom HVAC agents**
3. **Add model fine-tuning**
4. **Set up monitoring and alerting**
5. **Deploy to production environment**

## 🤝 Contributing

See the main project documentation for contribution guidelines.

## 📄 License

This project is part of the HVAC-Remix CRM system.
