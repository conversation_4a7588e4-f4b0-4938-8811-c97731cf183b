#!/bin/bash

# Enhanced HVAC Agent Protocol Setup with Bielik V3 and Gemma4
# This script sets up the complete system with LLM containers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    # Check for NVIDIA Docker (optional but recommended)
    if command -v nvidia-docker &> /dev/null; then
        success "NVIDIA Docker detected - GPU acceleration will be available"
    else
        warning "NVIDIA Docker not found - LLMs will run on CPU only"
    fi
    
    success "Prerequisites check completed"
}

# Stop existing containers
stop_existing() {
    log "Stopping existing containers..."
    docker-compose down --remove-orphans || true
    success "Existing containers stopped"
}

# Build and start the enhanced system
start_system() {
    log "Building and starting enhanced HVAC Agent Protocol system..."
    
    # Build custom images
    log "Building LLM manager..."
    docker-compose build llm-manager
    
    # Start infrastructure services first
    log "Starting infrastructure services..."
    docker-compose up -d postgres redis qdrant
    
    # Wait for infrastructure to be ready
    log "Waiting for infrastructure services to be ready..."
    sleep 10
    
    # Start LLM services
    log "Starting LLM services (this may take a while)..."
    docker-compose up -d bielik-v3 gemma4
    
    # Wait for LLM services to be ready
    log "Waiting for LLM services to initialize..."
    sleep 30
    
    # Download models
    log "Downloading LLM models (this will take significant time)..."
    docker-compose run --rm llm-manager
    
    # Start agent protocol server
    log "Starting Agent Protocol server..."
    docker-compose up -d agent-protocol-server
    
    # Start nginx
    log "Starting Nginx reverse proxy..."
    docker-compose up -d nginx
    
    success "Enhanced system started successfully!"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check container status
    log "Container status:"
    docker-compose ps
    
    # Check API endpoints
    log "Checking API endpoints..."
    
    # Agent Protocol API
    if curl -f http://localhost:8001/ &> /dev/null; then
        success "Agent Protocol API is responding"
    else
        error "Agent Protocol API is not responding"
    fi
    
    # Bielik V3
    if curl -f http://localhost:8877/api/tags &> /dev/null; then
        success "Bielik V3 API is responding"
    else
        warning "Bielik V3 API is not responding"
    fi
    
    # Gemma4
    if curl -f http://localhost:8878/api/tags &> /dev/null; then
        success "Gemma4 API is responding"
    else
        warning "Gemma4 API is not responding"
    fi
    
    # Qdrant
    if curl -f http://localhost:6333/health &> /dev/null; then
        success "Qdrant is responding"
    else
        warning "Qdrant is not responding"
    fi
}

# Display system information
show_info() {
    log "System Information:"
    echo ""
    echo "🚀 HVAC Agent Protocol Enhanced System"
    echo "======================================"
    echo ""
    echo "📡 API Endpoints:"
    echo "  • Agent Protocol API: http://localhost:8001"
    echo "  • API Documentation:  http://localhost:8001/docs"
    echo "  • Bielik V3 LLM:      http://localhost:8877"
    echo "  • Gemma4 LLM:         http://localhost:8878"
    echo "  • Qdrant Vector DB:   http://localhost:6333"
    echo "  • PostgreSQL:         localhost:5432"
    echo "  • Redis:              localhost:6379"
    echo ""
    echo "🔧 Management Commands:"
    echo "  • View logs:          docker-compose logs -f"
    echo "  • Stop system:        docker-compose down"
    echo "  • Restart:            docker-compose restart"
    echo "  • Update models:      docker-compose run --rm llm-manager"
    echo ""
    echo "📊 Monitoring:"
    echo "  • Container status:   docker-compose ps"
    echo "  • Resource usage:     docker stats"
    echo ""
}

# Main execution
main() {
    echo ""
    echo "🤖 HVAC Agent Protocol Enhanced Setup"
    echo "====================================="
    echo ""
    
    check_prerequisites
    stop_existing
    start_system
    
    # Wait a bit for services to stabilize
    log "Waiting for services to stabilize..."
    sleep 15
    
    health_check
    show_info
    
    success "Enhanced HVAC Agent Protocol system is ready!"
    echo ""
    echo "🎉 Next steps:"
    echo "  1. Visit http://localhost:8001/docs to explore the API"
    echo "  2. Test the LLM endpoints at ports 8877 and 8878"
    echo "  3. Check the logs with: docker-compose logs -f"
    echo ""
}

# Run main function
main "$@"
